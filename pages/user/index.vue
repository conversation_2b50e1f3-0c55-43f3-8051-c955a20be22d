<template>
  <view class="container" @touchmove.stop.prevent>
   <view class="fixed-header">
     <view class="head_bg">
       <image src="/static/appointment-bg.png"></image>
       <text class="head_title">我的</text>
     </view>
   </view>

    <!-- 用户信息区域 -->
    <view class="user-info-section">
      <view class="user-avatar">
        <!-- 根据 userInfo.avatar 是否有值决定显示图片还是图标 -->
        <template v-if="userInfo.avatar">
          <image :src="userInfo.avatar" mode="aspectFill"></image>
        </template>
        <template v-else>
          <uni-icons type="person" size="60" color="#2349A4"></uni-icons>
        </template>
      </view>
      <view class="user-details" @click="!hasLogin && navTo('/pages/public/login')">
        <text class="user-name">{{ userInfo.nickname || '未登录' }}</text>
        <text class="user-phone">{{ userInfo.userType === 'member' || !userInfo.userType ? userInfo.phone || '点击登录' : '' }}</text>
      </view>
      <view class="wx-bind-btn" v-if="hasLogin && !userInfo.wechatOpenid" @click="wxAuth">
        <uni-icons type="weixin" size="24" color="#ffffff"></uni-icons>
        <text class="wx-bind-text">绑定微信</text>
      </view>
      <view class="wx-bind-btn" v-if="hasLogin && userInfo.wechatOpenid">
        <uni-icons type="weixin" size="24" color="#ffffff"></uni-icons>
        <text class="wx-bind-text">已绑定微信</text>
      </view>
    </view>

    <!-- 功能列表 -->
    <view v-if="hasLogin" class="function-list">

      <view v-if="userInfo.userType === 'member'" class="function-item" @click="navTo('/pages/user/invite-code')">
        <text class="iconfont icon-invite"></text>
        <text class="function-text">邀请码</text>
        <text class="function-arrow">></text>
      </view>

      <view class="function-item" @click="navTo('/pages/user/change-password')">
        <text class="iconfont icon-password"></text>
        <text class="function-text">修改密码</text>
        <text class="function-arrow">></text>
      </view>

      <view class="function-item" @click="navTo('/pages/user/contact')">
        <text class="iconfont icon-service"></text>
        <text class="function-text">联系我们</text>
        <text class="function-arrow">></text>
      </view>

      <view class="function-item" @click="toLogout">
        <text class="iconfont icon-settings"></text>
        <text class="function-text">退出登录</text>
        <text class="function-arrow">></text>
      </view>
    </view>
  </view>
</template>
<script>

import {
  mapState, mapMutations
} from 'vuex';
import {getWxOpenid, updateWxOpenid, memberInfo} from "@/api/member";
export default {
  components: {
  },
  data(){
    return {
      coverTransform: 'translateY(0px)',
      coverTransition: '0s',
      moving: false,
      couponCount:null,
      hasShownWechatTip: false // 标记是否已经显示过微信绑定提示，避免重复显示
    }
  },
  onLoad(){
    // 每次加载页面时重置提示状态，允许重新检测微信绑定状态
    this.hasShownWechatTip = false;
  },
  async onShow(){
    // 每次显示页面时重置提示状态，允许重新检测微信绑定状态
    this.hasShownWechatTip = false;

    // 每次显示页面时，如果用户已登录，则获取最新的用户信息
    if (this.hasLogin) {
      try {
        if (this.userInfo.memberType === 'NORMAL') {
          const userInfoResponse = await memberInfo();
          this.login(userInfoResponse.data);
        }

        // 检测微信绑定状态并显示提示
        this.checkWechatBindingStatus();
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取用户信息失败，可能是token过期等问题，这里不显示错误提示
        // 因为用户可能只是想查看页面，不需要打断用户体验
      }
    }
  },
  // #ifndef MP
  onNavigationBarButtonTap(e) {
    const index = e.index;
    if (index === 0) {
      this.navTo('/pages/set/set');
    }else if(index === 1){
      // #ifdef APP-PLUS
      const pages = getCurrentPages();
      const page = pages[pages.length - 1];
      const currentWebview = page.$getAppWebview();
      currentWebview.hideTitleNViewButtonRedDot({
        index
      });
      // #endif
      uni.navigateTo({
        url: '/pages/notice/notice'
      })
    }
  },
  // #endif
  computed: {
    ...mapState(['hasLogin','userInfo']),
  },
  methods: {
    ...mapMutations(['logout', 'login']),
    /**
     * 统一跳转接口,拦截未登录路由
     * navigator标签现在默认没有转场动画，所以用view
     */
    navTo(url){
      if(!this.hasLogin){
        url = '/pages/public/login';
      }
      uni.navigateTo({
        url
      })
    },

    //退出登录
    toLogout(){
      uni.showModal({
        content: '确定要退出登录么',
        success: (e)=>{
          if(e.confirm){
            this.logout();
            setTimeout(()=>{
              uni.navigateBack();
            }, 200)
          }
        }
      });
    },
    wxAuth(){
      // 调用微信登录接口获取 code
      uni.login({
        provider: 'weixin', //使用微信登录
        success: async (loginRes) => {
          console.log(loginRes);
          if (loginRes) {
            try {
              // 调用后端接口完成微信登录
              const wxResponse = await getWxOpenid({code: loginRes.code});
              if (wxResponse.data) {
                const updateResponse = await updateWxOpenid({openid: wxResponse.data});
                console.log(updateResponse);
                if (updateResponse.code === 200) {
                  const userInfoResponse = await memberInfo()
                  this.login(userInfoResponse.data);
                  // 绑定成功后重置所有提示相关的设置
                  uni.removeStorageSync('neverShowWechatTip');
                  uni.removeStorageSync('tempSkipWechatTip');
                  uni.showToast({
                    title: '绑定成功',
                    icon: 'success',
                    duration: 2000
                  });

                } else {
                  uni.showToast({
                    title: updateResponse.data.message || '绑定失败',
                    icon: 'none',
                    duration: 2000
                  });
                }
              } else {
                uni.showToast({
                  title: '获取微信openid失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            } catch (error) {
              console.error('微信绑定错误:', error);
              uni.showToast({
                title: '绑定失败，请重试',
                icon: 'none',
                duration: 2000
              });
            }
          } else {
            uni.showToast({
              title: '获取微信登录凭证失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error);
          uni.showToast({
            title: '微信登录失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    // 检测微信绑定状态并显示提示
    checkWechatBindingStatus() {
      // 检查用户是否选择了不再提示
      const neverShowWechatTip = uni.getStorageSync('neverShowWechatTip');

      // 检查是否在临时跳过期间（30秒内）
      const tempSkipTime = uni.getStorageSync('tempSkipWechatTip');
      const now = Date.now();
      const isInTempSkipPeriod = tempSkipTime && (now - tempSkipTime < 30000); // 30秒内

      // 如果用户已登录且没有绑定微信，并且还没有显示过提示，且用户没有选择不再提示，且不在临时跳过期间
      if (this.hasLogin && !this.userInfo.wechatOpenid && !this.hasShownWechatTip && !neverShowWechatTip && !isInTempSkipPeriod) {
        // 延迟显示提示，确保页面已经完全加载
        setTimeout(() => {
          this.showWechatBindingOptions();
        }, 500); // 延迟500ms显示，确保页面渲染完成
      }
    },

    // 显示微信绑定选项
    showWechatBindingOptions() {
      // 首先显示提示信息
      uni.showModal({
        title: '微信绑定提示',
        content: '您还未绑定微信，将无法使用微信支付功能。建议您绑定微信以获得更好的支付体验。',
        confirmText: '立即绑定',
        cancelText: '稍后再说',
        success: (res) => {
          if (res.confirm) {
            // 用户选择立即绑定
            this.wxAuth();
          } else {
            // 用户选择稍后再说，显示进一步的选择
            this.showLaterOptions();
          }
        },
        fail: () => {
          // 用户取消操作，不做任何标记，下次还会提示
        }
      });
    },

    // 显示"稍后再说"的进一步选项
    showLaterOptions() {
      uni.showModal({
        title: '确认选择',
        content: '您选择了稍后绑定微信，请选择：',
        confirmText: '不再提示',
        cancelText: '下次再说',
        success: (res) => {
          if (res.confirm) {
            // 用户选择不再提示，永久不再显示
            uni.setStorageSync('neverShowWechatTip', true);
            this.hasShownWechatTip = true;
            uni.showToast({
              title: '已设置不再提示',
              icon: 'success',
              duration: 1500
            });
          } else {
            // 用户选择下次再说，仅本次访问不再显示，下次切换回来时会重新提示
            this.hasShownWechatTip = true;
            // 设置一个临时标记，在短时间内不再提示（比如30秒）
            uni.setStorageSync('tempSkipWechatTip', Date.now());
          }
        },
        fail: () => {
          // 用户取消操作，默认为下次再说
          this.hasShownWechatTip = true;
          uni.setStorageSync('tempSkipWechatTip', Date.now());
        }
      });
    }
  }
}
</script>
<style lang='scss'>
.container {
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #f5f5f5;
}

.fixed-header {
  position: relative;
  z-index: 10;
}

.head_bg {
  height: 200rpx;
  position: relative; /* 添加相对定位 */
  margin: 0 -20rpx; /* 抵消父容器的内边距 */
  width: calc(100% + 40rpx); /* 增加宽度以抵消内边距 */

  image {
    width: 100%;
    height: 100%;
  }

  .head_title {
    padding-top: 70rpx;
    position: absolute; /* 绝对定位 */
    top: 50%; /* 垂直居中 */
    left: 50%; /* 水平居中 */
    transform: translate(-50%, -50%); /* 精确居中 */
    color: #ffffff; /* 白色文字 */
    font-size: 36rpx; /* 字体大小 */
    z-index: 1; /* 确保文字在图片上层 */
  }
}

/* 用户信息区域样式 */
.user-info-section {
  margin: 30rpx;
  padding: 40rpx 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;

  .user-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 30rpx;
    border: 4rpx solid rgba(74, 102, 244, 0.2);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .user-details {
    flex: 1;

    .user-name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
      display: block;
    }

    .user-phone {
      font-size: 28rpx;
      color: #999;
      display: block;
    }
  }

  .wx-bind-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #07c160 0%, #00d976 100%);
    padding: 16rpx 24rpx;
    border-radius: 50rpx;
    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
    transition: all 0.3s ease;
    min-width: 160rpx;

    .wx-bind-text {
      color: #ffffff;
      font-size: 26rpx;
      font-weight: 500;
      margin-left: 8rpx;
    }

    &:active {
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4);
    }

    &:hover {
      box-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.4);
    }
  }
}

/* 功能列表样式 */
.function-list {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  .function-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .iconfont {
      font-size: 40rpx;
      color: #4a66f4;
      margin-right: 20rpx;
      width: 50rpx;
      text-align: center;
    }

    .function-text {
      flex: 1;
      font-size: 30rpx;
      color: #333;
    }

    .function-arrow {
      font-size: 30rpx;
      color: #ccc;
    }

    &:active {
      background-color: #f9f9f9;
    }
  }
}
</style>
