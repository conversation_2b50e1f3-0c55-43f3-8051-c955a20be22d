<template>
	<view class="content">
		<uni-icons
			type="weixin"
			color="#07C160"
			size="80"
			class="wechat-icon"
		></uni-icons>
		<text class="title">返回微信</text>
		<text class="subtitle">点击下方按钮打开微信应用</text>
		
		<view class="btn-group">
			<view class="wechat-btn" @click="openWechat">
				<uni-icons type="weixin" color="#fff" size="20" class="btn-icon"></uni-icons>
				<text class="btn-text">打开微信</text>
			</view>
			<view class="back-btn" @click="goBack">返回</view>
		</view>
	</view>
</template>

<script>
	import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
	
	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				
			}
		},
		onLoad(options) {
			console.log('打开微信页面加载');
		},
		methods: {
			// 打开微信应用
			openWechat() {
				console.log('尝试打开微信应用');
				
				// #ifdef APP-PLUS
				// App端使用URL Scheme打开微信
				plus.runtime.openURL('weixin://', function(res) {
					console.log('微信打开失败:', res);
					uni.showToast({
						title: '请确保已安装微信',
						icon: 'none',
						duration: 2000
					});
				});
				// #endif
				
				// #ifdef H5
				// H5端尝试使用URL Scheme
				try {
					window.location.href = 'weixin://';
					// 如果3秒后还在当前页面，说明可能没有安装微信
					setTimeout(() => {
						uni.showToast({
							title: '请在手机上打开或安装微信',
							icon: 'none',
							duration: 2000
						});
					}, 3000);
				} catch (error) {
					console.error('打开微信失败:', error);
					uni.showToast({
						title: '无法打开微信',
						icon: 'none',
						duration: 2000
					});
				}
				// #endif
				
				// #ifdef MP-WEIXIN
				// 微信小程序环境下不需要打开微信
				uni.showToast({
					title: '您已在微信中',
					icon: 'success',
					duration: 1500
				});
				// #endif
			},
			
			// 返回上一页
			goBack() {
				// 检查是否可以返回上一页
				const pages = getCurrentPages();
				if (pages.length > 1) {
					uni.navigateBack();
				} else {
					// 如果没有上一页，跳转到首页
					uni.reLaunch({
						url: '/pages/appointment/index'
					});
				}
			}
		}
	}
</script>

<style lang='scss'>
	.content {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		min-height: 100vh;
		padding: 40rpx;
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	}
	
	.wechat-icon {
		margin-bottom: 40rpx;
		animation: pulse 2s infinite;
	}
	
	@keyframes pulse {
		0% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.1);
		}
		100% {
			transform: scale(1);
		}
	}
	
	.title {
		font-size: 48rpx;
		color: #303133;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.subtitle {
		font-size: 28rpx;
		color: #909399;
		margin-bottom: 80rpx;
		text-align: center;
	}
	
	.btn-group {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
	}
	
	.wechat-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 600rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #07C160 0%, #05a050 100%);
		border-radius: 50rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.3);
		transition: all 0.3s ease;
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.3);
		}
	}
	
	.btn-icon {
		margin-right: 16rpx;
	}
	
	.btn-text {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
	}
	
	.back-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 600rpx;
		height: 80rpx;
		font-size: 30rpx;
		color: #606266;
		background: #fff;
		border: 1px solid #dcdfe6;
		border-radius: 40rpx;
		transition: all 0.3s ease;
		
		&:active {
			background: #f5f7fa;
			transform: translateY(1rpx);
		}
	}
</style>
