<template>
	<view class="upload-content">
		<block v-for="(item, index) in imageList" :key="index">
			<view class="upload-item">
				<image
					class="upload-img"
					:src="item.src || item.filePath"
					mode="aspectFill"
					@click="previewImage(index)"
				></image>
				<image
					v-if="!readonly"
					class="upload-del-btn"
					@click="delImage(index)"
					src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RjNBODgzQjUwNDM5MTFFOUJDMjlGN0UwRTJGMjVCNjQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RjNBODgzQjYwNDM5MTFFOUJDMjlGN0UwRTJGMjVCNjQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGM0E4ODNCMzA0MzkxMUU5QkMyOUY3RTBFMkYyNUI2NCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGM0E4ODNCNDA0MzkxMUU5QkMyOUY3RTBFMkYyNUI2NCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuYQTIAAAAHNSURBVHjazJgxasMwFIbjnsD4BDmCLxDQDZrNQ5bcILlBvXvpDRLIWmiHFkyH2t0LyVbwEkPpbE/eYlUCGYQax096UuIffkIgij7ryU/vyZuYy2e+Z56K70R8lsJcOfPn5AriMCvmZ2YKdMW8EQ/hRA9iEorwXlpZtObMRySQ6kysvrFWloHUEIcmUBuHUDLccmxQskErt7wyVLdy00tQ4Q2g5De2V9kNwWjffiNDA33f/42i6E13Qj5uNptlwJD+SyODuaqqqi/KVBTFOxSKA7VtW/NxSZI8AcbE2nuraZpvKgSBk6G4drsdBOwogz1CwAghH/JEl+BUKJ1VltPHHjoIAoeE4l53YFqb+RKcBSgqyqXhtxEKZwkKB3YOThYCqjPuCDoHZwGK3mGLtdPp5LmqTI1Dqe4p26EkNqA4iE6eg2z+iQ0okyQMATvYgLIIt9Y6knTyFBIudHqIq3BpmkL60VJ9O0sXZY8MBwSLtdNGEAQ/i8XiVXcz83EcEPDbuq/fzMdYWt+6GTmMsX2rh9q3TtsxNrzXhKt1rwg6rR1Dhdjqo7QMlWOvoWTF4imxQMRF7eaL8L5ohmwrLgDB8pCQc8DlcG7y538CDABJNGPqfaJgfgAAAABJRU5ErkJggg=="
					mode="scaleToFill">
				</image>
			</view>
		</block>
		<view
			class="upload-add-btn"
			v-if="rduLength > 0 && !readonly"
			@click="chooseImage"
		></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			imageList: []
		};
	},
	props: {
		url: {
			type: String,
			value: '' //上传接口地址
		},
		count: {
			type: Number,
			value: 4 //单次可选择的图片数量
		},
		length: {
			type: Number,
			value: 50 //可上传总数量
		},
		initialImages: {  // 添加一个 props 来接收初始图片列表
			type: Array,
			default: () => []
		},
		readonly: {
			type: Boolean,
			default: false
		},
		// 添加文件类型限制属性
		fileExtname: {
			type: Array,
			default: () => ['jpg', 'jpeg', 'png']
		}
	},
	watch: {
		initialImages: {
			handler(newVal) {
				this.imageList = [...newVal];
			},
			immediate: true
		}
	},
	computed: {
		rduLength(){
			return this.length - this.imageList.length;
		}
	},
	methods: {
		//选择图片
		chooseImage: function(){
			uni.chooseImage({
				count: this.rduLength < this.count ? this.rduLength : this.count, //最多可以选择的图片张数，默认9
				sizeType: ['original', 'compressed'], //original 原图，compressed 压缩图，默认二者都有
				sourceType: ['album'], //album 从相册选图，camera 使用相机，默认二者都有
				success: async (res) => {
          const images = res.tempFilePaths;
          // 添加文件类型验证
          const validImages = this.validateFileTypes(res.tempFiles);
          if (validImages.length > 0) {
            try {
              console.log('🚀 [图片处理] 开始批量压缩图片:', {
                总数量: validImages.length,
                图片列表: validImages
              });

              // 压缩图片
              const compressedImages = await Promise.all(
                  validImages.map((path, index) => {
                    console.log(`📸 [图片处理] 压缩第${index + 1}张图片:`, path);
                    return this.compressImage(path);
                  })
              );

              console.log('✅ [图片处理] 所有图片压缩完成:', {
                压缩数量: compressedImages.length,
                成功率: '100%'
              });

              this.uploadFiles(compressedImages);
            } catch (err) {
              console.error('❌ [图片处理] 压缩失败:', err);
              uni.showToast({
                title: '图片压缩失败',
                icon: 'none'
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
			});
		},
    compressImage: function(filePath) {
      console.log('🖼️ [图片压缩] 开始压缩图片:', filePath);

      return new Promise((resolve, reject) => {
        // 首先获取原始文件大小
        this.getFileSize(filePath).then(originalSize => {
          console.log('📊 [图片压缩] 原始文件大小:', (originalSize / 1024).toFixed(2) + 'KB');

          // 根据文件大小决定压缩策略
          const strategy = this.getCompressionStrategy(originalSize);
          console.log('🎯 [图片压缩] 压缩策略:', strategy);

          if (strategy.action === 'skip') {
            console.log('✅ [图片压缩] 文件小于500KB，跳过压缩');
            resolve(filePath);
            return;
          }

          const img = new Image();
          img.src = filePath;

          img.onload = () => {
            console.log('📏 [图片压缩] 原始图片尺寸:', {
              width: img.width,
              height: img.height,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight
            });

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // 设置压缩参数
            const MAX_WIDTH = 1200;   // 最大宽度
            const MAX_HEIGHT = 1200;  // 最大高度

            let width = img.width;
            let height = img.height;
            let needsResize = false;

            console.log('⚙️ [图片压缩] 压缩配置:', {
              maxWidth: MAX_WIDTH,
              maxHeight: MAX_HEIGHT,
              strategy: strategy.description,
              targetQuality: strategy.quality,
              originalWidth: width,
              originalHeight: height
            });

            // 计算压缩后的尺寸
            if (width > MAX_WIDTH || height > MAX_HEIGHT) {
              needsResize = true;
              const ratio = Math.min(MAX_WIDTH / width, MAX_HEIGHT / height);
              width = Math.round(width * ratio);
              height = Math.round(height * ratio);
              console.log('📐 [图片压缩] 需要缩放，比例:', ratio.toFixed(3));
            } else {
              console.log('📐 [图片压缩] 尺寸符合要求，无需缩放');
            }

            console.log('📏 [图片压缩] 压缩后尺寸:', {
              width: Math.round(width),
              height: Math.round(height)
            });

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            // 根据策略进行压缩
            this.compressWithStrategy(canvas, strategy, needsResize, resolve, filePath);
          };

          img.onerror = (error) => {
            console.error('❌ [图片压缩] 图片加载失败:', error);
            console.log('🔄 [图片压缩] 返回原图路径');
            resolve(filePath); // 压缩失败返回原图
          };
        }).catch(error => {
          console.error('❌ [图片压缩] 获取文件大小失败:', error);
          console.log('🔄 [图片压缩] 返回原图路径');
          resolve(filePath);
        });
      });
    },

    // 获取文件大小
    getFileSize: function(filePath) {
      return new Promise((resolve, reject) => {
        // 如果是blob URL，通过fetch获取大小
        if (filePath.startsWith('blob:')) {
          fetch(filePath)
            .then(response => response.blob())
            .then(blob => resolve(blob.size))
            .catch(reject);
        } else {
          // 其他情况，尝试通过Image对象估算
          const img = new Image();
          img.onload = () => {
            // 估算文件大小（这是一个粗略估算）
            const estimatedSize = img.width * img.height * 3; // RGB 3字节
            resolve(estimatedSize);
          };
          img.onerror = reject;
          img.src = filePath;
        }
      });
    },

    // 获取压缩策略
    getCompressionStrategy: function(fileSize) {
      const size500KB = 500 * 1024;
      const size1MB = 1024 * 1024;

      if (fileSize < size500KB) {
        return {
          action: 'skip',
          description: '文件小于500KB，跳过压缩',
          quality: 1.0
        };
      } else if (fileSize >= size500KB && fileSize < size1MB) {
        return {
          action: 'light',
          description: '文件500KB-1MB，轻度压缩',
          quality: 1.0,
          targetSize: null
        };
      } else {
        return {
          action: 'heavy',
          description: '文件大于1MB，压缩到500KB',
          quality: 0.8,
          targetSize: size500KB
        };
      }
    },

    // 根据策略压缩
    compressWithStrategy: function(canvas, strategy, needsResize, resolve, fallbackPath) {
      if (strategy.action === 'light') {
        // 轻度压缩：固定质量压缩一次
        console.log('🔧 [图片压缩] 执行轻度压缩，质量:', strategy.quality);
        canvas.toBlob(blob => {
          if (blob) {
            const sizeKB = blob.size / 1024;
            console.log('✅ [图片压缩] 轻度压缩完成:', {
              最终大小: sizeKB.toFixed(2) + 'KB',
              质量: strategy.quality,
              是否缩放: needsResize
            });

            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => resolve(fallbackPath);
            reader.readAsDataURL(blob);
          } else {
            console.error('❌ [图片压缩] Blob创建失败，返回原图');
            resolve(fallbackPath);
          }
        }, 'image/jpeg', strategy.quality);
      } else if (strategy.action === 'heavy') {
        // 重度压缩：直接压缩到目标大小
        console.log('🔧 [图片压缩] 执行重度压缩，目标大小:', (strategy.targetSize / 1024).toFixed(0) + 'KB');
        canvas.toBlob(blob => {
          if (blob) {
            const sizeKB = blob.size / 1024;
            console.log('✅ [图片压缩] 重度压缩完成:', {
              最终大小: sizeKB.toFixed(2) + 'KB',
              目标大小: (strategy.targetSize / 1024).toFixed(0) + 'KB',
              质量: strategy.quality,
              是否缩放: needsResize
            });

            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => resolve(fallbackPath);
            reader.readAsDataURL(blob);
          } else {
            console.error('❌ [图片压缩] Blob创建失败，返回原图');
            resolve(fallbackPath);
          }
        }, 'image/jpeg', strategy.quality);
      }
    },

    // 动态质量压缩方法（保留，以防需要）
    compressWithQuality: function(canvas, maxSize, needsResize, resolve, fallbackPath) {
      let quality = 0.9; // 初始质量
      let attempt = 0;
      const maxAttempts = 5;

      const tryCompress = () => {
        attempt++;
        console.log(`🔄 [图片压缩] 第${attempt}次压缩尝试，质量: ${quality}`);

        canvas.toBlob(blob => {
          if (blob) {
            const sizeKB = blob.size / 1024;
            console.log('📊 [图片压缩] 当前结果:', {
              尝试次数: attempt,
              文件大小: sizeKB.toFixed(2) + 'KB',
              目标大小: (maxSize / 1024).toFixed(0) + 'KB',
              质量: quality,
              是否缩放: needsResize
            });

            // 如果文件大小符合要求或已达到最大尝试次数
            if (blob.size <= maxSize || attempt >= maxAttempts || quality <= 0.3) {
              console.log('✅ [图片压缩] 压缩完成:', {
                最终大小: sizeKB.toFixed(2) + 'KB',
                最终质量: quality,
                压缩比: needsResize ? '已缩放' : '仅质量压缩',
                总尝试次数: attempt
              });

              const reader = new FileReader();
              reader.onload = () => {
                console.log('✅ [图片压缩] 转换为DataURL成功');
                resolve(reader.result);
              };
              reader.onerror = () => {
                console.error('❌ [图片压缩] DataURL转换失败');
                resolve(fallbackPath);
              };
              reader.readAsDataURL(blob);
            } else {
              // 文件还是太大，降低质量重试
              quality = Math.max(0.3, quality - 0.15);
              console.log('📉 [图片压缩] 文件过大，降低质量重试');
              tryCompress();
            }
          } else {
            console.error('❌ [图片压缩] Blob创建失败，返回原图');
            resolve(fallbackPath);
          }
        }, 'image/jpeg', quality);
      };

      tryCompress();
    },

		// 添加文件类型验证方法
		validateFileTypes(files) {
			const validFiles = [];
			const invalidFiles = [];

			// 创建MIME类型到扩展名的映射
			const mimeToExt = {
				'image/jpeg': 'jpg/jpeg',
				'image/jpg': 'jpg',
				'image/png': 'png',
				'image/gif': 'gif',
				'image/bmp': 'bmp',
				'image/webp': 'webp',
				'application/pdf': 'pdf'
				// 可以根据需要添加更多映射
			};

			// 创建允许的MIME类型列表
			const allowedMimeTypes = this.fileExtname.map(ext => {
				if (ext === 'jpg' || ext === 'jpeg') return ['image/jpeg', 'image/jpg'];
				if (ext === 'png') return 'image/png';
				if (ext === 'gif') return 'image/gif';
				if (ext === 'bmp') return 'image/bmp';
				if (ext === 'webp') return 'image/webp';
				if (ext === 'pdf') return 'application/pdf';
				// 可以根据需要添加更多映射
				return `image/${ext}`;
			}).flat();

			console.log('允许的MIME类型:', allowedMimeTypes);

			files.forEach(file => {
				console.log('文件信息:', file);

				if (allowedMimeTypes.includes(file.type)) {
					validFiles.push(file.path);
				} else {
					// 获取易读的文件类型名称
					const readableType = mimeToExt[file.type] || file.type.split('/')[1] || file.type;
					invalidFiles.push(readableType);
				}
			});

			// 如果有无效文件，显示提示
			if (invalidFiles.length > 0) {
				// 去重，避免重复的类型
				const uniqueInvalidTypes = [...new Set(invalidFiles)];

				uni.showToast({
					title: `不支持${uniqueInvalidTypes.join('、')}格式，仅支持${this.fileExtname.join('、')}`,
					icon: 'none',
					duration: 3000
				});
			}

			return validFiles;
		},
		//上传图片
		async uploadFiles(images) {
			for (let i = 0; i < images.length; i++) {
				const newImage = {
					src: images[i],
					filePath: images[i]
				};

				try {
					const uploadResult = await this.uploadImage(images[i]);
					newImage.src = uploadResult;
					newImage.filePath = uploadResult;

					// 添加到图片列表
					this.imageList.push(newImage);

					// 通知父组件上传成功
					this.$emit('success', {
						code: 200,
						data: uploadResult
					});
				} catch (err) {
					console.error('Upload failed:', err);
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}
			}
		},
		uploadImage: function(file) {
			return new Promise((resolve, reject)=> {
				const token = uni.getStorageSync('token');
				const formData = {
					thumb_mode: 1,
				};
				this.uploadTask = uni.uploadFile({
					url: this.url,
					filePath: file,
					name: 'file',
					header: {
						'Authorization': token
					},
					formData: formData,
					success(uploadFileResult) {
						const uploadFileRes = JSON.parse(uploadFileResult.data) || {};
						if(uploadFileRes.code === 200 && uploadFileRes.data) {
							// 确保返回图片URL
							resolve(uploadFileRes.data);
						} else {
							reject('接口返回错误');
						}
					},
					fail() {
						reject('网络链接错误');
					}
				});
			});
		},
		//删除图片
		delImage(index) {
			uni.showModal({
				content: '确定要放弃这张图片么？',
				success: (confirmRes) => {
					if (confirmRes.confirm) {
						const deletedImage = this.imageList[index];
						this.imageList.splice(index, 1);
						// 触发删除事件，通知父组件
						this.$emit('delete', {
							index,
							image: deletedImage
						});
					}
				}
			});
		},
		//预览图片
		previewImage: function(index){
			const urls = [];
			this.imageList.forEach((item)=> {
				urls.push(item.filePath);
			})
			uni.previewImage({
				current: urls[index],
				urls: urls,
				indicator: "number"
			})
		}
	}
}
</script>

<style lang="scss">
.upload-content {
  padding: 24rpx 28rpx;
  background-color: #fff;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.upload-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1;

  .upload-img {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    object-fit: cover;
  }

  .upload-del-btn {
    position: absolute;
    right: -16rpx;
    top: -14rpx;
    width: 36rpx;
    height: 36rpx;
    border: 4rpx solid #fff;
    border-radius: 100rpx;
  }

  .upload-progress {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    font-size: 24rpx;
    border-radius: 8rpx;
  }
}

.upload-add-btn {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 8rpx;
  background: #f9f9f9;

  &:before,
  &:after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4rpx;
    height: 60rpx;
    background-color: #d6d6d6;
  }

  &:after {
    width: 60rpx;
    height: 4rpx;
  }

  &:active {
    background-color: #f7f7f7;
  }
}
</style>
