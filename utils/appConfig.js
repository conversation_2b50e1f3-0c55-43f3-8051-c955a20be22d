// appConfig.js

// 环境配置
const ENV = {
  development: {
    API_URL: 'https://ts.shengdaochejian.top/portal-backend',
    UPLOAD_URL: 'https://ts.shengdaochejian.top/portal-backend/minio/upload'
  },
  test: {
    API_URL: 'https://ts.shengdaochejian.top/portal-backend',
    UPLOAD_URL: 'https://ts.shengdaochejian.top/portal-backend/minio/upload'
  },
  production: {
    API_URL: 'https://www.shengdaochejian.top/portal-backend',
    UPLOAD_URL: 'https://www.shengdaochejian.top/portal-backend/oss/upload'
  }
};

// 当前环境
const CURRENT_ENV = process.env.NODE_ENV === 'production'
  ? (process.env.VUE_APP_MODE === 'test' ? 'test' : 'production')
  : 'development';

// 根据当前环境导出API基础路径
export const API_BASE_URL = ENV[CURRENT_ENV].API_URL;

// 导出上传地址
export const UPLOAD_URL = ENV[CURRENT_ENV].UPLOAD_URL;

//是否启用支付宝支付
export const USE_ALIPAY = false;

// 导出当前环境名称（可用于调试）
export const CURRENT_ENVIRONMENT = CURRENT_ENV;
