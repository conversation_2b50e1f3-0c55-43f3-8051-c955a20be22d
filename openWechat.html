<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打开微信</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .wechat-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        .title {
            font-size: 32px;
            color: #303133;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .subtitle {
            font-size: 16px;
            color: #909399;
            margin-bottom: 50px;
            line-height: 1.5;
        }

        .btn-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            gap: 20px;
        }

        .wechat-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            height: 60px;
            background: linear-gradient(135deg, #07C160 0%, #05a050 100%);
            border: none;
            border-radius: 30px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 8px 20px rgba(7, 193, 96, 0.3);
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .wechat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(7, 193, 96, 0.4);
        }

        .wechat-btn:active {
            transform: translateY(1px);
            box-shadow: 0 4px 10px rgba(7, 193, 96, 0.3);
        }

        .btn-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            fill: white;
        }

        .back-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            height: 50px;
            background: #fff;
            border: 1px solid #dcdfe6;
            border-radius: 25px;
            color: #606266;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .back-btn:hover {
            background: #f5f7fa;
        }

        .back-btn:active {
            transform: translateY(1px);
        }

        .toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            z-index: 1000;
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .title {
                font-size: 28px;
            }
            
            .subtitle {
                font-size: 14px;
            }
            
            .wechat-btn, .back-btn {
                max-width: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 微信图标 -->
        <svg class="wechat-icon" viewBox="0 0 1024 1024" fill="#07C160">
            <path d="M331.5 235c-89.5 0-162 65.5-162 146s72.5 146 162 146c27 0 52.5-6 75.5-16.5l75.5 40.5-20-67c50-36.5 82-91.5 82-153 0-80.5-72.5-146-162-146z m-65 186c-18 0-32.5-14.5-32.5-32.5s14.5-32.5 32.5-32.5 32.5 14.5 32.5 32.5-14.5 32.5-32.5 32.5z m130 0c-18 0-32.5-14.5-32.5-32.5s14.5-32.5 32.5-32.5 32.5 14.5 32.5 32.5-14.5 32.5-32.5 32.5z"/>
            <path d="M677.5 475c-12.5 0-24.5 1.5-36 4-7.5-87-85-156-179-156-105.5 0-191 77-191 172 0 53.5 28.5 101.5 74 133.5l-15 56 70.5-35c23.5 7 48.5 11 74.5 11 8.5 0 17-0.5 25-1.5 16.5 71 85.5 124 168 124 20 0 39.5-3 58-8.5l58.5 29.5-12.5-46.5c37.5-26.5 61.5-67.5 61.5-113.5 0-80.5-72.5-146-162-146z m-97.5 97.5c-12.5 0-22.5-10-22.5-22.5s10-22.5 22.5-22.5 22.5 10 22.5 22.5-10 22.5-22.5 22.5z m97.5 0c-12.5 0-22.5-10-22.5-22.5s10-22.5 22.5-22.5 22.5 10 22.5 22.5-10 22.5-22.5 22.5z"/>
        </svg>

        <h1 class="title">返回微信</h1>
        <p class="subtitle">点击下方按钮打开微信应用</p>

        <div class="btn-group">
            <button class="wechat-btn" onclick="openWechat()">
                <svg class="btn-icon" viewBox="0 0 1024 1024">
                    <path d="M331.5 235c-89.5 0-162 65.5-162 146s72.5 146 162 146c27 0 52.5-6 75.5-16.5l75.5 40.5-20-67c50-36.5 82-91.5 82-153 0-80.5-72.5-146-162-146z m-65 186c-18 0-32.5-14.5-32.5-32.5s14.5-32.5 32.5-32.5 32.5 14.5 32.5 32.5-14.5 32.5-32.5 32.5z m130 0c-18 0-32.5-14.5-32.5-32.5s14.5-32.5 32.5-32.5 32.5 14.5 32.5 32.5-14.5 32.5-32.5 32.5z"/>
                    <path d="M677.5 475c-12.5 0-24.5 1.5-36 4-7.5-87-85-156-179-156-105.5 0-191 77-191 172 0 53.5 28.5 101.5 74 133.5l-15 56 70.5-35c23.5 7 48.5 11 74.5 11 8.5 0 17-0.5 25-1.5 16.5 71 85.5 124 168 124 20 0 39.5-3 58-8.5l58.5 29.5-12.5-46.5c37.5-26.5 61.5-67.5 61.5-113.5 0-80.5-72.5-146-162-146z m-97.5 97.5c-12.5 0-22.5-10-22.5-22.5s10-22.5 22.5-22.5 22.5 10 22.5 22.5-10 22.5-22.5 22.5z m97.5 0c-12.5 0-22.5-10-22.5-22.5s10-22.5 22.5-22.5 22.5 10 22.5 22.5-10 22.5-22.5 22.5z"/>
                </svg>
                打开微信
            </button>
            <button class="back-btn" onclick="goBack()">返回</button>
        </div>
    </div>

    <!-- Toast 提示 -->
    <div id="toast" class="toast"></div>

    <script>
        // 显示提示信息
        function showToast(message, duration = 2000) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.style.display = 'block';
            
            setTimeout(() => {
                toast.style.display = 'none';
            }, duration);
        }

        // 打开微信应用
        function openWechat() {
            console.log('尝试打开微信应用');
            
            // 检测设备类型
            const userAgent = navigator.userAgent.toLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isAndroid = /android/.test(userAgent);
            const isWeChat = /micromessenger/.test(userAgent);
            
            // 如果已经在微信中
            if (isWeChat) {
                showToast('您已在微信中', 1500);
                return;
            }
            
            try {
                if (isIOS) {
                    // iOS 使用 URL Scheme
                    window.location.href = 'weixin://';
                    // 备用方案：尝试打开微信的通用链接
                    setTimeout(() => {
                        window.location.href = 'https://itunes.apple.com/cn/app/wechat/id414478124';
                    }, 2000);
                } else if (isAndroid) {
                    // Android 使用 Intent URL
                    window.location.href = 'intent://dl/business/?ticket=ta4c0c2d1a1e8bc4b#Intent;scheme=weixin;package=com.tencent.mm;end';
                    // 备用方案
                    setTimeout(() => {
                        window.location.href = 'weixin://';
                    }, 1000);
                } else {
                    // 桌面端或其他设备
                    window.location.href = 'weixin://';
                }
                
                // 3秒后如果还在当前页面，提示用户
                setTimeout(() => {
                    showToast('请确保已安装微信应用', 3000);
                }, 3000);
                
            } catch (error) {
                console.error('打开微信失败:', error);
                showToast('无法打开微信，请手动打开微信应用');
            }
        }

        // 返回上一页
        function goBack() {
            // 尝试返回上一页
            if (window.history.length > 1) {
                window.history.back();
            } else {
                // 如果没有历史记录，可以跳转到指定页面或关闭窗口
                showToast('没有可返回的页面');
                // 或者跳转到首页
                // window.location.href = '/';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('打开微信页面加载完成');
            
            // 可以在这里添加页面加载后的逻辑
            // 比如从URL参数获取信息等
            const urlParams = new URLSearchParams(window.location.search);
            const from = urlParams.get('from');
            
            if (from) {
                console.log('来源页面:', from);
            }
        });
    </script>
</body>
</html>
